{
  "name": "SMS Testing Workflow",
  "nodes": [
    {
      "parameters": {},
      "id": "manual-trigger-test",
      "name": "Manual Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [
        240,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Generate sample lead data for testing\nconst sampleLeads = [\n  {\n    business_name: \"Kavinė Vilnius\",\n    address: \"Gedimino pr. 15, Vilnius\",\n    google_phone: \"+***********\",\n    owner_phone: \"\",\n    website: \"\",\n    phone_source: \"google\",\n    sms_sent: \"pending\",\n    sms_status: \"\",\n    reply_received: \"no\",\n    notes: \"Lead from Google Maps - no website found\",\n    google_maps_url: \"https://maps.google.com/example1\",\n    category: \"Restaurant\",\n    rating: \"4.2\",\n    reviews_count: \"45\"\n  },\n  {\n    business_name: \"Grožio salonas Kaunas\",\n    address: \"Laisvės al. 25, Kaunas\",\n    google_phone: \"+***********\",\n    owner_phone: \"\",\n    website: \"\",\n    phone_source: \"google\",\n    sms_sent: \"pending\",\n    sms_status: \"\",\n    reply_received: \"no\",\n    notes: \"Lead from Google Maps - no website found\",\n    google_maps_url: \"https://maps.google.com/example2\",\n    category: \"Beauty Salon\",\n    rating: \"4.8\",\n    reviews_count: \"123\"\n  },\n  {\n    business_name: \"Autoservisas Klaipėda\",\n    address: \"Taikos pr. 10, Klaipėda\",\n    google_phone: \"+***********\",\n    owner_phone: \"\",\n    website: \"\",\n    phone_source: \"google\",\n    sms_sent: \"pending\",\n    sms_status: \"\",\n    reply_received: \"no\",\n    notes: \"Lead from Google Maps - no website found\",\n    google_maps_url: \"https://maps.google.com/example3\",\n    category: \"Auto Repair\",\n    rating: \"4.0\",\n    reviews_count: \"67\"\n  }\n];\n\nconsole.log(`Generated ${sampleLeads.length} sample leads for testing`);\nreturn sampleLeads.map(lead => ({ json: lead }));"
      },
      "id": "sample-data-generator",
      "name": "Generate Sample Leads",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        460,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Mock SMS Response Simulator - for testing without sending actual SMS\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  const leadData = item.json;\n  \n  // Simulate different SMS responses (70% success, 30% failure for testing)\n  const isSuccess = Math.random() > 0.3; // 70% success rate\n  \n  let mockSmsResponse;\n  \n  if (isSuccess) {\n    // Mock successful SMS response\n    mockSmsResponse = {\n      success: true,\n      status: 'success',\n      code: 200,\n      message: 'SMS sent successfully',\n      messageId: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      timestamp: new Date().toISOString()\n    };\n  } else {\n    // Mock failed SMS response\n    const failureReasons = [\n      'Invalid phone number',\n      'Network timeout',\n      'Insufficient credits',\n      'Phone number blocked',\n      'Invalid message content'\n    ];\n    \n    mockSmsResponse = {\n      success: false,\n      status: 'failed',\n      code: 400,\n      error: failureReasons[Math.floor(Math.random() * failureReasons.length)],\n      timestamp: new Date().toISOString()\n    };\n  }\n  \n  // Combine original lead data with mock SMS response\n  const combinedData = {\n    ...leadData,\n    sms_response: mockSmsResponse\n  };\n  \n  processedItems.push({ json: combinedData });\n  \n  console.log(`Mock SMS for ${leadData.business_name}: ${isSuccess ? 'SUCCESS' : 'FAILED'} - ${mockSmsResponse.message || mockSmsResponse.error}`);\n}\n\nconsole.log(`Simulated SMS sending for ${processedItems.length} leads`);\nreturn processedItems;"
      },
      "id": "mock-sms-simulator",
      "name": "Mock SMS Simulator",\n      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        680,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Process SMS response and prepare data for Google Sheets update\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  // Now the SMS response is in sms_response property, and original data is preserved\n  const originalData = item.json;\n  const smsResponse = item.json.sms_response || item.json; // Fallback to full json if sms_response not found\n  \n  // Determine SMS status based on response\n  let smsStatus = 'failed';\n  let smsStatusDetails = '';\n  \n  // Check if SMS was sent successfully\n  if (smsResponse.success === true || smsResponse.status === 'success' || smsResponse.code === 200) {\n    smsStatus = 'success';\n    smsStatusDetails = 'SMS sent successfully';\n  } else if (smsResponse.success === false || smsResponse.error) {\n    smsStatus = 'failed';\n    smsStatusDetails = smsResponse.error || smsResponse.message || 'SMS sending failed';\n  } else {\n    // If response structure is different, check for common success indicators\n    const responseStr = JSON.stringify(smsResponse).toLowerCase();\n    if (responseStr.includes('success') || responseStr.includes('sent')) {\n      smsStatus = 'success';\n      smsStatusDetails = 'SMS sent successfully';\n    } else {\n      smsStatus = 'failed';\n      smsStatusDetails = 'Unknown SMS response: ' + JSON.stringify(smsResponse);\n    }\n  }\n  \n  // Prepare updated data - now we have access to the original business data\n  const updatedData = {\n    business_name: originalData.business_name,\n    address: originalData.address,\n    google_phone: originalData.google_phone,\n    owner_phone: originalData.owner_phone,\n    website: originalData.website,\n    phone_source: originalData.phone_source,\n    reply_received: originalData.reply_received,\n    notes: originalData.notes,\n    google_maps_url: originalData.google_maps_url,\n    category: originalData.category,\n    rating: originalData.rating,\n    reviews_count: originalData.reviews_count,\n    sms_sent: smsStatus,\n    sms_status: smsStatusDetails,\n    sms_sent_date: new Date().toISOString().split('T')[0],\n    phone_used: originalData.google_phone || originalData.owner_phone || 'no_phone',\n    sms_response_raw: JSON.stringify(smsResponse) // Keep raw response for debugging\n  };\n  \n  processedItems.push({ json: updatedData });\n}\n\nconsole.log(`Processed ${processedItems.length} SMS responses`);\nreturn processedItems;"
      },
      "id": "process-sms-response-test",
      "name": "Process SMS Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        900,
        300
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "condition1",
              "leftValue": "={{ $json.sms_sent }}",
              "rightValue": "success",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "check-sms-success-test",
      "name": "Check SMS Success",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2.2,
      "position": [
        1120,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Display successful SMS results\nconst items = $input.all();\n\nconsole.log('=== SUCCESSFUL SMS RESULTS ===');\nfor (const item of items) {\n  const data = item.json;\n  console.log(`✅ ${data.business_name}`);\n  console.log(`   Phone: ${data.phone_used}`);\n  console.log(`   Status: ${data.sms_status}`);\n  console.log(`   Date: ${data.sms_sent_date}`);\n  console.log(`   Address: ${data.address}`);\n  console.log('---');\n}\n\nconsole.log(`Total successful SMS: ${items.length}`);\nreturn $input.all();"
      },
      "id": "display-success-results",
      "name": "Display Success Results",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1340,
        200
      ]
    },
    {
      "parameters": {
        "jsCode": "// Display failed SMS results\nconst items = $input.all();\n\nconsole.log('=== FAILED SMS RESULTS ===');\nfor (const item of items) {\n  const data = item.json;\n  console.log(`❌ ${data.business_name}`);\n  console.log(`   Phone: ${data.phone_used}`);\n  console.log(`   Status: ${data.sms_status}`);\n  console.log(`   Date: ${data.sms_sent_date}`);\n  console.log(`   Address: ${data.address}`);\n  console.log('---');\n}\n\nconsole.log(`Total failed SMS: ${items.length}`);\nreturn $input.all();"
      },
      "id": "display-failed-results",
      "name": "Display Failed Results",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1340,
        400
      ]
    }
  ],
  "connections": {
    "Manual Trigger": {
      "main": [
        [
          {
            "node": "Generate Sample Leads",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Sample Leads": {
      "main": [
        [
          {
            "node": "Mock SMS Simulator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Mock SMS Simulator": {
      "main": [
        [
          {
            "node": "Process SMS Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process SMS Response": {
      "main": [
        [
          {
            "node": "Check SMS Success",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check SMS Success": {
      "main": [
        [
          {
            "node": "Display Success Results",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Display Failed Results",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "test-version-1",
  "meta": {
    "templateCredsSetupCompleted": true
  },
  "id": "sms-testing-workflow",
  "tags": []
}
